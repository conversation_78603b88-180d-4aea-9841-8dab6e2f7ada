﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>warnings</Nullable>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <Configurations>Debug;Release;Commercial</Configurations>
    <Platforms>x64</Platforms>
	<UseWindowsForms>true</UseWindowsForms>
  </PropertyGroup>

	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Commercial|x64'">
		<Optimize>True</Optimize>
	</PropertyGroup>

	<ItemGroup>
		<EmbeddedResource Include="..\Resources\visible.bundle" />
		<EmbeddedResource Include="..\Resources\visibilitycheck.bundle" />
		<EmbeddedResource Include="..\Resources\vicheckglowpmc.bundle" />
		<EmbeddedResource Include="..\Resources\vischeckflat.bundle" />
		<EmbeddedResource Include="..\Resources\vischeckflatpmc.bundle" />
		<EmbeddedResource Include="..\Resources\wireframe.bundle" />
		<EmbeddedResource Include="..\Resources\wireframepmc.bundle" />
		<EmbeddedResource Include="..\Resources\FILE_CRYPT_KEY.bin" />
		<EmbeddedResource Include="..\Resources\NeoSansStdRegular.otf" />
		<EmbeddedResource Include="..\Resources\NeoSansStdMedium.otf" />
		<EmbeddedResource Include="..\Resources\NeoSansStdBold.otf" />
		<EmbeddedResource Include="..\Resources\NeoSansStdItalic.otf" />
		<EmbeddedResource Include="..\Resources\DEFAULT_DATA.json" />
	</ItemGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <DebugType>none</DebugType>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <DebugType>none</DebugType>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Commercial|x64'">
    <DebugType>none</DebugType>
  </PropertyGroup>

  <ItemGroup>
	  <PackageReference Include="Microsoft.AspNetCore.SignalR.Protocols.MessagePack" Version="9.0.0" />
	  <PackageReference Include="Microsoft.Bcl.Cryptography" Version="9.0.0" />
	  <PackageReference Include="SkiaSharp.Views.WindowsForms" Version="2.88.9" />
	  <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
	  <PackageReference Include="Reloaded.Assembler" Version="1.0.16" />
	  <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.0" />
	  <PackageReference Include="Svg.Skia" Version="2.0.0.4" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="VmmFrost">
      <HintPath>..\VmmFrost.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
